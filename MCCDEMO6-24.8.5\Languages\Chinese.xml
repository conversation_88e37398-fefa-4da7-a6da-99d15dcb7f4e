﻿<?xml version="1.0" encoding="utf-8" ?>
<Chinese>
	<MainFrm_NoSystemParaXMLFileInExecuteDir>可执行目录下没有参数文件[SystemPara.xml]!</MainFrm_NoSystemParaXMLFileInExecuteDir>
	<MainFrm_NoSystemParaXMLFileInExecuteDirUseDefaultPara>程序执行目录中没有发现参数文件[SystemPara.xml]！使用默认参数!</MainFrm_NoSystemParaXMLFileInExecuteDirUseDefaultPara>
	<MainFrm_DecodeXmlFileOK>解析XML文件成功！</MainFrm_DecodeXmlFileOK>
	<MainFrm_DecodeXmlFileFailed>解析XML文件出错，请确定参数文件中记录的数值的正确性！</MainFrm_DecodeXmlFileFailed>
	<MainFrm_PulseDLIs0ResetParaAndRestart>轴的脉冲当量参数为0，设重设参数后，重启软件！</MainFrm_PulseDLIs0ResetParaAndRestart>
	<MainFrm_PositiveLimitEnableStop>警告！系统正限位有效，停止运行！</MainFrm_PositiveLimitEnableStop>
	<MainFrm_NegtiveLimitEnableStop>警告！系统负限位有效，停止运行！</MainFrm_NegtiveLimitEnableStop>
	<MainFrm_AllProgrameHasSended>程序发送完毕！</MainFrm_AllProgrameHasSended>	
	<MainFrm_SendSomeProgrameSucceed>下发第{0:d3}次循环的第{1:d3}条指令成功！</MainFrm_SendSomeProgrameSucceed>
	<MainFrm_SendSomeProgrameFailed>下发第{0:d3}次循环的第{1:d3}条指令失败，检查通信联接！</MainFrm_SendSomeProgrameFailed>
	<MainFrm_ParameterDownloading>参数下载中...[{0:p}]</MainFrm_ParameterDownloading>
	<MainFrm_ParameterDownloadFailed>参数下载失败！</MainFrm_ParameterDownloadFailed>
	<MainFrm_EncoderParameterDownloading>编码器参数下载中...[{0:p}]</MainFrm_EncoderParameterDownloading>
	<MainFrm_EncoderParameterDownloadFailed>编码器参数下载失败！</MainFrm_EncoderParameterDownloadFailed>
	<MainFrm_SystemParameterUploading>轴参数上传中...[{0:p}]！</MainFrm_SystemParameterUploading>
	<MainFrm_SystemParameterUploadFailed>轴参数上传失败！</MainFrm_SystemParameterUploadFailed>
	<MainFrm_EncoderParameterUploading>编码器参数上传中...[{0:p}]</MainFrm_EncoderParameterUploading>
	<MainFrm_EncoderarameterUploadFailed>编码器参数上传失败！</MainFrm_EncoderarameterUploadFailed>
	<MainFrm_ChooseProgrameLineToEdit>请选择要修改的程序行！</MainFrm_ChooseProgrameLineToEdit>
	<MainFrm_OpenProgrameFileSucceed>成功打开[{0}]程序文件</MainFrm_OpenProgrameFileSucceed>
	<MainFrm_SaveProgrameFileTo>成功保存程序至</MainFrm_SaveProgrameFileTo>
	<MainFrm_SaveProgrameFileFailedBeacuseNoPrigrame>程序区为空，不能保存！</MainFrm_SaveProgrameFileFailedBeacuseNoPrigrame>
	<MainFrm_ConnectMCCardFailed>联接于[{0}]运动控制卡初始化失败！</MainFrm_ConnectMCCardFailed>
	<MainFrm_ConnectMCCardSucceed>联接于[{0}]运动控制卡初成功！</MainFrm_ConnectMCCardSucceed>	
	<MainFrm_CloseMCCardSucceed>关闭通讯端口成功！</MainFrm_CloseMCCardSucceed>	
	<MainFrm_CloseMCCardFailed>关闭通讯端口失败！</MainFrm_CloseMCCardFailed>
	<MainFrm_NoSysParaFileFristOpenItFirst>没有参数文件，请首先打开参数文件！</MainFrm_NoSysParaFileFristOpenItFirst>
	<MainFrm_UnloadFailed>卸载失败！执行目录下没有相应的参数文件！</MainFrm_UnloadFailed>
	<MainFrm_ProgrameRunningCanNotStartAutoRunAgain>系统正在运行或已经开始程序控制，不能重新启动自动程序控制！</MainFrm_ProgrameRunningCanNotStartAutoRunAgain>
	<MainFrm_SendProgrameCommandFailedCheckComunication>发送指令失败！检查通讯联接！</MainFrm_SendProgrameCommandFailedCheckComunication>
	<MainFrm_UpdateSystemParameterFailed>更新轴参数失败[</MainFrm_UpdateSystemParameterFailed>
	<MainFrm_UpdateSystemParameterSucceed>更新轴参数成功！</MainFrm_UpdateSystemParameterSucceed>
	<MainFrm_UpdateEncoderParameterFailed>更新编码器参数失败[</MainFrm_UpdateEncoderParameterFailed>
	<MainFrm_UpdateEncoderParameterSucceed>更新编码器参数成功！</MainFrm_UpdateEncoderParameterSucceed>
	<MainFrm_TCPConnectFailedCheckIPAndPort>联接于[{0}]地址[{1}]端口的运动控制卡初始化失败！请检查IP地址和端口号！</MainFrm_TCPConnectFailedCheckIPAndPort>	
	<MainFrm_TCPConnectSucceed>联接于[{0}]地址[{1}]端口的运动控制卡成功！</MainFrm_TCPConnectSucceed>	
	<MainFrm_TCPCloseFailed>关闭网络联接失败！</MainFrm_TCPCloseFailed>	
	<MainFrm_TCPCloseSucceed>关闭网络联接成功！</MainFrm_TCPCloseSucceed>

	<TestIOFrm_TCPCloseSucceed>负向电压值[0-{0:f2}]V,正向电压值[{1:f2}-3.3]V</TestIOFrm_TCPCloseSucceed>

	<SysCtrlParaFrm_SaveSystemControlParaFailed>保存系统参数到控制器失败！</SysCtrlParaFrm_SaveSystemControlParaFailed>
	<SysCtrlParaFrm_SetLCDScanDelayParaFailed>设置屏幕扫描延时参数失败！</SysCtrlParaFrm_SetLCDScanDelayParaFailed>
	<SysCtrlParaFrm_SetResetParameterFailed>设置恢复出厂设置参数失败！</SysCtrlParaFrm_SetResetParameterFailed>
	<SysCtrlParaFrm_SetHandWheelFailed>设置手轮模式参数失败！</SysCtrlParaFrm_SetHandWheelFailed>
	<SysCtrlParaFrm_ParameterFormatError>参数格式有误，请检查后重新设置！</SysCtrlParaFrm_ParameterFormatError>
	<SysCtrlParaFrm_ReadSystemControlFailed>读配置参数失败！</SysCtrlParaFrm_ReadSystemControlFailed>

	<USARTAssitantFrm_OperateSucceed>成功!</USARTAssitantFrm_OperateSucceed>
	<USARTAssitantFrm_OperateFailed>失败!</USARTAssitantFrm_OperateFailed>
	<USARTAssitantFrm_OpenPort>打开{0}</USARTAssitantFrm_OpenPort>
	<USARTAssitantFrm_ClosePort>关闭{0}</USARTAssitantFrm_ClosePort>

	<CacSinglLevFrm_StateOpen>常开</CacSinglLevFrm_StateOpen>
	<CacSinglLevFrm_StateClose>常闭</CacSinglLevFrm_StateClose>
	<CacSinglLevFrm_StateEnable>有效</CacSinglLevFrm_StateEnable>
	<CacSinglLevFrm_StateDisable>无效</CacSinglLevFrm_StateDisable>
	<CacSinglLevFrm_NegLimit>负限位</CacSinglLevFrm_NegLimit>
	<CacSinglLevFrm_PosLimit>正限位</CacSinglLevFrm_PosLimit>
	<CacSinglLevFrm_Original>原点</CacSinglLevFrm_Original>
	<CacSinglLevFrm_ComTable>补偿表</CacSinglLevFrm_ComTable>
	<CacSinglLevFrm_HardLimit>硬限位</CacSinglLevFrm_HardLimit>
	<CacSinglLevFrm_SoftLimit>软限位</CacSinglLevFrm_SoftLimit>
	
	<TriggerMoveFrm_TestEnd>测试结束！</TriggerMoveFrm_TestEnd>
	<TriggerMoveFrm_TriggerCntError>脉冲数错误！</TriggerMoveFrm_TriggerCntError>
	
	<ManualOpExFrm_CommandModeDist>距离</ManualOpExFrm_CommandModeDist>
	<ManualOpExFrm_CommandModePos>位置</ManualOpExFrm_CommandModePos>
	<ManualOpExFrm_MoveParameterError>输入的运动参数有错，请重新输入！</ManualOpExFrm_MoveParameterError>
	<ManualOpExFrm_NotSupportGroupMoveInJogMode>点动模式下，不支持组合运动！</ManualOpExFrm_NotSupportGroupMoveInJogMode>
	<ManualOpExFrm_NotChooseAxisInGroupMode>没有选择组合运动的轴！</ManualOpExFrm_NotChooseAxisInGroupMode>
	
	<InputFunCfgFrm_ReadInputCfgParaError>读取控制系统上的输入扩展功能配置参数失败！重新更新！</InputFunCfgFrm_ReadInputCfgParaError>
	<InputFunCfgFrm_SetLow16BitsInputFunFailed>设置低16位输入扩展功能参数失败！</InputFunCfgFrm_SetLow16BitsInputFunFailed>
	<InputFunCfgFrm_SetHigh16BitsInputFunFailed>设置高16位输入扩展功能参数失败！</InputFunCfgFrm_SetHigh16BitsInputFunFailed>
	<InputFunCfgFrm_SaveParameterToControllerFailed>保存参数到控制器失败！</InputFunCfgFrm_SaveParameterToControllerFailed>

	<AxisExParaFrm_ReadAxisParameterFailed>读扩展参数失败！</AxisExParaFrm_ReadAxisParameterFailed>
	<AxisExParaFrm_SetAxisParameterFailed>设置扩展参数失败！</AxisExParaFrm_SetAxisParameterFailed>
	<AxisExParaFrm_ParameterFormatError>输入数值格式不正确！</AxisExParaFrm_ParameterFormatError>

	<ExPosFollowFrm_DataError>数据文件出错！第[{0}]行！每行数据必须有6个轴的位置，并用TAB间隔！</ExPosFollowFrm_DataError>
	<ExPosFollowFrm_DecodeFileDone>文件解析完毕，共有[{0}]个数据点！</ExPosFollowFrm_DecodeFileDone>
	<ExPosFollowFrm_ExceptionRiseWhenDecodeFile>解析文件过程出现异常！</ExPosFollowFrm_ExceptionRiseWhenDecodeFile>
	<ExPosFollowFrm_OpenComunicationPortSucceed>端口打开成功！</ExPosFollowFrm_OpenComunicationPortSucceed>
	<ExPosFollowFrm_OpenComunicationPortFailed>端口打开失败!请确认该串口是否正在被其它程序占用！</ExPosFollowFrm_OpenComunicationPortFailed>
	<ExPosFollowFrm_ClosePortSucceed>关闭串口成功！</ExPosFollowFrm_ClosePortSucceed>
	<ExPosFollowFrm_ClosePortFailed>关闭串口失败！</ExPosFollowFrm_ClosePortFailed>
	<ExPosFollowFrm_StopSendExPositionPoint>停止下传外部跟踪数据点！</ExPosFollowFrm_StopSendExPositionPoint>
	<ExPosFollowFrm_SomeCommandSendedOKAndCurrentCount>{0} 指令位置发送完成！CurrerCnt = {1}</ExPosFollowFrm_SomeCommandSendedOKAndCurrentCount>
	<ExPosFollowFrm_SomeCommandSendedFailed>串口发送数据失败，停止发送，关闭定时器！[{0}]</ExPosFollowFrm_SomeCommandSendedFailed>
	
	<ScopeFrm_ScopeVersionInfo>示波器工具版本：</ScopeFrm_ScopeVersionInfo>
	<ScopeFrm_NoChannelPleaseConfigThemFirst>示波器没有配置通道，请先配置通道！</ScopeFrm_NoChannelPleaseConfigThemFirst>
	<ScopeFrm_SendStartSampleCommandFailed>发送开始采集数据指令失败！</ScopeFrm_SendStartSampleCommandFailed>
	<ScopeFrm_SendSomeChannelConfigCommandFailed>发送第{0}号通道配置示波器指令失败！</ScopeFrm_SendSomeChannelConfigCommandFailed>
	<ScopeFrm_ConfigAllChannelSucceed>配置数据采集通道成功！</ScopeFrm_ConfigAllChannelSucceed>
	<ScopeFrm_NotConfigChannelCanNotDownloadChannelParameters>没有配置数据采集通道，不能下载通道参数！</ScopeFrm_NotConfigChannelCanNotDownloadChannelParameters>
	<ScopeFrm_DeleteScopConfigFailed>发送删除示波器指令失败！</ScopeFrm_DeleteScopConfigFailed>
	<ScopeFrm_CloseScopFailed>发送关闭示波器指令失败！</ScopeFrm_CloseScopFailed>
	<ScopeFrm_SaveScopeConfigFileSucceed>保存示波器通道配置文件[{0}]成功！</ScopeFrm_SaveScopeConfigFileSucceed>
	<ScopeFrm_LoadScopeConfigFileSucceed>读入示波器通道配置文件[{0}]成功！</ScopeFrm_LoadScopeConfigFileSucceed>
	<ScopeFrm_ConfigFileEmpty>选择的示波器通道配置文件成功为空，请重新选择！</ScopeFrm_ConfigFileEmpty>
	<ScopeFrm_WordChannel>通道</ScopeFrm_WordChannel>
	<ScopeFrm_ChannelConfigDonDownloadConfigParameters>通道配置完成，下载示波器配置信息...</ScopeFrm_ChannelConfigDonDownloadConfigParameters>
	<ScopeFrm_OpenAndDeleteAllConfigInfo>打开示波器设备，删除原有配置！</ScopeFrm_OpenAndDeleteAllConfigInfo>
	<ScopeFrm_OpenScopeFailed>发送打开示波器指令失败！</ScopeFrm_OpenScopeFailed>
	<ScopeFrm_StartDownloadConfigParametes>下载示波器配置信息！</ScopeFrm_StartDownloadConfigParametes>
	<ScopeFrm_SaveFileTo>成功保存程序至</ScopeFrm_SaveFileTo>
	<ScopeFrm_CanNotSaveFileBecauseFileNameNull>文件名空，不能保存！</ScopeFrm_CanNotSaveFileBecauseFileNameNull>
	<ScopeFrm_SaveFileFailed>文件保存不成功!</ScopeFrm_SaveFileFailed>
	<ScopeFrm_OpenFileSucceed>成功打开[{0}]程序文件</ScopeFrm_OpenFileSucceed>
	<ScopeFrm_WordNotConfig>未配置</ScopeFrm_WordNotConfig>
	<ScopeFrm_SendStopSampleCommandFailed>发送停止采集数据指令失败！</ScopeFrm_SendStopSampleCommandFailed>
	<ScopeFrm_OpenPortFailed>打开{0}失败！</ScopeFrm_OpenPortFailed>
	<ScopeFrm_OpenPortSucceed>打开{0}成功！</ScopeFrm_OpenPortSucceed>
	<ScopeFrm_ClosePortSucceed>关闭串口成功！</ScopeFrm_ClosePortSucceed>
	<ScopeFrm_ClosePortFailed>关闭串口失败！</ScopeFrm_ClosePortFailed>
	<ScopeFrm_SaveHistoryDataSucceed>保存历史数据成功！</ScopeFrm_SaveHistoryDataSucceed>

	<CurveDefineFrm_ChooseCurveBeforeDownloadCurveParameters>在下载曲线参数前,先选择曲线！</CurveDefineFrm_ChooseCurveBeforeDownloadCurveParameters>
	<CurveDefineFrm_SendSomeParameterFailed>下发第{0}个参数时,失败！</CurveDefineFrm_SendSomeParameterFailed>
	<CurveDefineFrm_SendMovementParameterSucceed>下发曲线运动参数成功！</CurveDefineFrm_SendMovementParameterSucceed>
	<CurveDefineFrm_PleaseDefineMasterAndSlave>请选择主从轴 \r\n</CurveDefineFrm_PleaseDefineMasterAndSlave>
	<CurveDefineFrm_DefineGearRatio>请输入紧电子齿轮比例 \r\n</CurveDefineFrm_DefineGearRatio>
	<CurveDefineFrm_SendGearParameterSucceed>下发电子齿轮参数成功，主轴{0} 从轴{1} \r\n</CurveDefineFrm_SendGearParameterSucceed>
	<CurveDefineFrm_SendGearParameterFailed>下发电子齿轮参数失败！</CurveDefineFrm_SendGearParameterFailed>
	<CurveDefineFrm_ChooseSlaveAxis>请选择从轴 \r\n</CurveDefineFrm_ChooseSlaveAxis>
	<CurveDefineFrm_SendGearInCommandSucceed>下发启动耦合指令成功！</CurveDefineFrm_SendGearInCommandSucceed>
	<CurveDefineFrm_SendGearInCommandFailed>下发启动耦合指令失败！</CurveDefineFrm_SendGearInCommandFailed>
	<CurveDefineFrm_SendGearOutCommandSucceed>下发停止耦合指令成功！</CurveDefineFrm_SendGearOutCommandSucceed>
	<CurveDefineFrm_SendGearOutCommandFailed>下发停止耦合指令失败！</CurveDefineFrm_SendGearOutCommandFailed>
	<CurveDefineFrm_SendCamInCommandFailed>下发启动曲线运动指令失败！</CurveDefineFrm_SendCamInCommandFailed>
	<CurveDefineFrm_SendCamInCommandSucceed>下发启动曲线运动指令成功！</CurveDefineFrm_SendCamInCommandSucceed>
	<CurveDefineFrm_SendCamOutCommandFailed>下发停止曲线运动指令失败！</CurveDefineFrm_SendCamOutCommandFailed>
	<CurveDefineFrm_SendCamOutCommandSucceed>下发停止曲线运动指令成功！</CurveDefineFrm_SendCamOutCommandSucceed>
	<CurveDefineFrm_NotAllParameterDefined>参数填写不完全！</CurveDefineFrm_NotAllParameterDefined>
	<CurveDefineFrm_DefineMovementPointCntAndT>描述运动的点数：{0}，周期为{1}！</CurveDefineFrm_DefineMovementPointCntAndT>
	<CurveDefineFrm_DataError>数据有误</CurveDefineFrm_DataError>
	<CurveDefineFrm_SetCamParameterFailed>设置轮廓运动参数失败！</CurveDefineFrm_SetCamParameterFailed>
	<CurveDefineFrm_SetCamParameterSucceed>设置轮廓运动参数成功！</CurveDefineFrm_SetCamParameterSucceed>
	<CurveDefineFrm_CamProfileEmpty>轮廓曲线为空，不能保存！</CurveDefineFrm_CamProfileEmpty>

	<ComParaFrm_WordComTable>补偿表</ComParaFrm_WordComTable>
	<ComParaFrm_WordCom>补偿</ComParaFrm_WordCom>
	<ComParaFrm_WordEnable>有效</ComParaFrm_WordEnable>
	<ComParaFrm_WordDisable>无效</ComParaFrm_WordDisable>
	<ComParaFrm_SetParameterFailed>设置参数失败！</ComParaFrm_SetParameterFailed>
	<ComParaFrm_ConnectMCCard>请联接串口！</ComParaFrm_ConnectMCCard>
	<ComParaFrm_ParameterIndxeError>参数序号有误！</ComParaFrm_ParameterIndxeError>
	<ComParaFrm_EnablePlatformCompensate>激活平面补偿功能成功！</ComParaFrm_EnablePlatformCompensate>
	<ComParaFrm_EnablePlatformCompensateFailed>激活平面补偿功能失败！</ComParaFrm_EnablePlatformCompensateFailed>
	<ComParaFrm_DisablePlatformCompensate>停止平面补偿功能！</ComParaFrm_DisablePlatformCompensate>
	<ComParaFrm_DisablePlatformCompensateFailed>停止平面补偿功能失败！</ComParaFrm_DisablePlatformCompensateFailed>

	<CacPulseDLFrm_DataFormatError>输入的数据有误，请检查！</CacPulseDLFrm_DataFormatError>

	<AbsEncFrm_EncoderFormatError>编码方式错误</AbsEncFrm_EncoderFormatError>
	<AbsEncFrm_CheckFrequencyParamereUnitIsHz>输入的频率值过小，请核对参数，注意单位是HZ</AbsEncFrm_CheckFrequencyParamereUnitIsHz>
	<AbsEncFrm_ValueTooLargeCheckInput>参数不合适，计算出的数值过大，请重新确认参数</AbsEncFrm_ValueTooLargeCheckInput>
	
	<TMC2209Frm_ReadSomeRegError>读取{0}号寄存器出错！</TMC2209Frm_ReadSomeRegError>
	<TMC2209Frm_NotDefineMicroSteps200StepPerTurn>未设置细分，电机每转200个脉冲！</TMC2209Frm_NotDefineMicroSteps200StepPerTurn>
	<TMC2209Frm_ReadMicroOkButFormatInValidPleaseReSet>读回细分数成功[{0}]，但数据不符合格式，请重新设置！</TMC2209Frm_ReadMicroOkButFormatInValidPleaseReSet>
	<TMC2209Frm_ReadMicroStepFailed>读取电机驱动细分数失败！</TMC2209Frm_ReadMicroStepFailed>
	<TMC2209Frm_ReadWorkCurrentFailed>读取电机驱动工作电流参数失败！</TMC2209Frm_ReadWorkCurrentFailed>
	<TMC2209Frm_ReadHoldCurrentFailed>读取电机驱动保持电流参数失败！</TMC2209Frm_ReadHoldCurrentFailed>
	<TMC2209Frm_InputFunctionParameterError>输入{0}模式配置参数有误，请重新设置！</TMC2209Frm_InputFunctionParameterError>
	<TMC2209Frm_ReadInputFunctionParameterFailed>读取电机驱动输入功能配置参数失败！</TMC2209Frm_ReadInputFunctionParameterFailed>
	<TMC2209Frm_RegValueFormatError>寄存器数据格式错误，不是{0}格式！</TMC2209Frm_RegValueFormatError>
	<TMC2209Frm_WriteRegSucceed>写{0}号寄存器成功！</TMC2209Frm_WriteRegSucceed>
	<TMC2209Frm_WriteRegFailed>写{0}号寄存器失败！</TMC2209Frm_WriteRegFailed>
	<TMC2209Frm_EnableDriverFailed>驱动使能失败！</TMC2209Frm_EnableDriverFailed>
	<TMC2209Frm_DisableDriverFailed>驱动去使能失败！</TMC2209Frm_DisableDriverFailed>
	<TMC2209Frm_SaveRegValueFailed>保存寄存器值失败！</TMC2209Frm_SaveRegValueFailed>
	<TMC2209Frm_SaveParameterToBoardFailed>保存运动控制板卡参数失败！</TMC2209Frm_SaveParameterToBoardFailed>
	<TMC2209Frm_ResetDriverFailed>驱动器复位失败！</TMC2209Frm_ResetDriverFailed>
	<TMC2209Frm_SetMicroStepFailed>设置驱动器细分数失败！</TMC2209Frm_SetMicroStepFailed>
	<TMC2209Frm_SetInputFunctionFailed>设置输入[{0}]模式失败！</TMC2209Frm_SetInputFunctionFailed>
	<TMC2209Frm_SetWorkCurrentFailed>设置驱动器工作电流失败！</TMC2209Frm_SetWorkCurrentFailed>
	<TMC2209Frm_SetHoldCurrentFailed>设置驱动器保持电流失败！</TMC2209Frm_SetHoldCurrentFailed>
  
  <about_Version>版本号：</about_Version>
  <about_dllVersion>DLL文件版本号：</about_dllVersion>
  <about_cardVersion>控制卡版本号：</about_cardVersion>
  <about_demoName>高速6轴闭环补偿控制器</about_demoName>
  <about_readFailed>读取配置信息失败！</about_readFailed>

  <axisComTable_showPosProperty>AxisId={0:d2}正向补偿表, 定义段数={1:d3}，周期性={2:d3}, 表单双性={3:d3}, 距离模值={4:f3}</axisComTable_showPosProperty>
  <axisComTable_showNegProperty>AxisId={0:d2}正向补偿表, 定义段数={1:d3}，周期性={2:d3}, 表单双性={3:d3}, 距离模值={4:f3}</axisComTable_showNegProperty>
  <axisComTable_PropertyError>读轴{0:d2}补偿表信息失败！</axisComTable_PropertyError>
  <axisComTable_SectionError>参数有误，有效的段数大于360</axisComTable_SectionError>
  <axisComTable_ValueError>读轴{0:d2}补偿表参数失败！</axisComTable_ValueError>

  <ARForm_ChooseFileSavePos>选择文件保存位置</ARForm_ChooseFileSavePos>
  <ARForm_Get>获取（读）</ARForm_Get>
  <ARForm_Save>保存（写）</ARForm_Save>
  <ARForm_ExecuteSuccessfully>执行成功！</ARForm_ExecuteSuccessfully>
  <ARForm_ExecuteFailed>执行失败！</ARForm_ExecuteFailed>
  <ARForm_ChooseARPrograme>请选择欲修改的程序行！</ARForm_ChooseARPrograme>
  <ARForm_ChooseCommand>请选择指令！</ARForm_ChooseCommand>
  <ARForm_NoCommandInEditBox>编辑框中没有指令！</ARForm_NoCommandInEditBox>
  <ARForm_GetFileFromPos>{0}号文件</ARForm_GetFileFromPos>;
  <ARForm_ResOK>成功</ARForm_ResOK>
  <ARForm_ResErr>失败</ARForm_ResErr>
  <ARForm_SaveFileTo>保存文件至</ARForm_SaveFileTo>
  <ARForm_ProgrameListEmpty>程序区为空，不能保存（或执行）！</ARForm_ProgrameListEmpty>
  <ARForm_RunFile>运行</ARForm_RunFile>
  <ARForm_Exception>异常</ARForm_Exception>
  <ARForm_AxisIDError>轴号错误，运动指令的轴号不能大于5</ARForm_AxisIDError>
  <ARForm_CommandPos>输入位置指令</ARForm_CommandPos>
  <ARForm_CommandSpd>输入速度指令</ARForm_CommandSpd>
</Chinese>